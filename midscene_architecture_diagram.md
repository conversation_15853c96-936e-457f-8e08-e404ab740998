# Midscene 组件架构图

## 整体架构

```mermaid
graph TB
    subgraph "用户层"
        A[测试脚本]
        B[Chrome扩展]
        C[Playground UI]
    end
    
    subgraph "@midscene/web"
        D[Playwright集成]
        E[Puppeteer集成]
        F[Bridge Mode]
        G[Chrome Extension]
        H[YAML解析器]
        I[Agent模块]
    end
    
    subgraph "@midscene/core"
        J[Executor引擎]
        K[Insight AI引擎]
        L[任务调度器]
        M[AI模型管理]
    end
    
    subgraph "@midscene/shared"
        N[图像处理]
        O[文件系统]
        P[工具函数]
        Q[类型定义]
        R[环境配置]
    end
    
    subgraph "@midscene/recorder"
        S[录制引擎]
        T[React UI]
        U[脚本生成器]
    end
    
    subgraph "外部服务"
        V[OpenAI API]
        W[Anthropic API]
        X[浏览器]
        Y[文件系统]
    end
    
    A --> D
    A --> E
    B --> G
    C --> H
    
    D --> J
    E --> J
    F --> J
    G --> J
    H --> J
    I --> J
    
    J --> K
    K --> M
    M --> V
    M --> W
    
    J --> N
    J --> O
    J --> P
    J --> Q
    J --> R
    
    S --> T
    S --> U
    U --> H
    
    J --> X
    N --> Y
    O --> Y
```

## 详细组件关系

### 1. 核心组件 (@midscene/core)

```mermaid
graph LR
    subgraph "Core Engine"
        A[Executor]
        B[Insight]
        C[Task Scheduler]
        D[AI Model Manager]
    end
    
    subgraph "AI Services"
        E[OpenAI Client]
        F[Anthropic Client]
        G[LangChain]
    end
    
    subgraph "Task Types"
        H[Locate Task]
        I[Extract Task]
        J[Assert Task]
        K[Action Task]
    end
    
    A --> B
    B --> D
    D --> E
    D --> F
    D --> G
    
    A --> H
    A --> I
    A --> J
    A --> K
```

### 2. Web自动化组件 (@midscene/web)

```mermaid
graph TB
    subgraph "Browser Automation"
        A[Playwright Agent]
        B[Puppeteer Agent]
        C[Chrome Extension]
    end
    
    subgraph "AI Integration"
        D[AI Locator]
        E[AI Extractor]
        F[AI Assertion]
        G[AI Action]
    end
    
    subgraph "Utilities"
        H[UI Utils]
        I[Bridge Mode]
        J[YAML Parser]
        K[Report Generator]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    E --> F
    F --> G
    
    A --> H
    B --> H
    C --> H
    
    H --> I
    I --> J
    J --> K
```

### 3. 共享工具库 (@midscene/shared)

```mermaid
graph LR
    subgraph "Image Processing"
        A[Sharp]
        B[Jimp]
        C[Photon]
        D[Image Analysis]
    end
    
    subgraph "File System"
        E[FS Utils]
        F[Path Utils]
        G[Config Manager]
    end
    
    subgraph "Utilities"
        H[Logger]
        I[Constants]
        J[Types]
        K[Environment]
    end
    
    A --> D
    B --> D
    C --> D
    
    E --> F
    F --> G
    
    H --> I
    I --> J
    J --> K
```

### 4. 录制组件 (@midscene/recorder)

```mermaid
graph TB
    subgraph "Recording Engine"
        A[Event Listener]
        B[Action Parser]
        C[Script Generator]
    end
    
    subgraph "React UI"
        D[Recording Panel]
        E[Script Editor]
        F[Preview Panel]
    end
    
    subgraph "Output"
        G[YAML Script]
        H[JavaScript Code]
        I[Test Cases]
    end
    
    A --> B
    B --> C
    C --> G
    C --> H
    C --> I
    
    D --> A
    E --> C
    F --> A
```

## 数据流架构

### 输入处理流程

```mermaid
flowchart TD
    A[自然语言指令] --> B[AI解析器]
    B --> C[任务生成器]
    C --> D[执行计划]
    D --> E[浏览器操作]
    E --> F[结果收集]
    F --> G[AI验证]
    G --> H[报告生成]
```

### 元素定位流程

```mermaid
flowchart LR
    A[页面截图] --> B[图像分析]
    B --> C[AI视觉识别]
    C --> D[元素坐标]
    D --> E[定位验证]
    E --> F[操作执行]
```

### 数据提取流程

```mermaid
flowchart TD
    A[提取指令] --> B[AI分析]
    B --> C[提取策略]
    C --> D[JavaScript执行]
    D --> E[数据收集]
    E --> F[结构化输出]
```

## 技术栈依赖关系

```mermaid
graph TB
    subgraph "AI Dependencies"
        A[OpenAI SDK]
        B[Anthropic SDK]
        C[LangChain]
    end
    
    subgraph "Browser Dependencies"
        D[Playwright]
        E[Puppeteer]
        F[Chrome DevTools]
    end
    
    subgraph "Image Processing"
        G[Sharp]
        H[Jimp]
        I[Photon]
    end
    
    subgraph "Build Tools"
        J[TypeScript]
        K[Modern.js]
        L[Vitest]
    end
    
    A --> C
    B --> C
    D --> F
    E --> F
    G --> I
    H --> I
    J --> K
    K --> L
```

## 部署架构

```mermaid
graph TB
    subgraph "Development"
        A[TypeScript源码]
        B[Modern.js构建]
        C[Vitest测试]
    end
    
    subgraph "Distribution"
        D[NPM包]
        E[Chrome扩展]
        F[CLI工具]
    end
    
    subgraph "Runtime"
        G[Node.js环境]
        H[浏览器环境]
        I[AI服务]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    
    D --> G
    E --> H
    F --> G
    G --> I
    H --> I
```

这个架构设计展示了Midscene作为一个完整的AI驱动UI自动化测试框架的各个组件之间的关系和数据流。每个组件都有明确的职责分工，通过标准化的接口进行通信，确保了系统的可扩展性和可维护性。 