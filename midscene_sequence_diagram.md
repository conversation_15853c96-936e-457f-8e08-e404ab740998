# Midscene 项目时序图分析

## 项目概述
Midscene 是一个基于AI的UI自动化测试框架，包含四个主要组件：
- `@midscene/core`: 核心AI引擎
- `@midscene/web`: Web自动化组件
- `@midscene/shared`: 共享工具库
- `@midscene/recorder`: 录制组件

## 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Test as 测试脚本
    participant Web as @midscene/web
    participant Core as @midscene/core
    participant Shared as @midscene/shared
    participant AI as AI服务
    participant Browser as 浏览器
    participant Recorder as @midscene/recorder

    Note over User,Recorder: 初始化阶段
    User->>Test: 编写测试脚本
    Test->>Web: 导入PlaywrightAiFixture
    Web->>Core: 初始化Executor
    Core->>Shared: 加载共享工具
    Shared->>AI: 配置AI模型(OpenAI/Anthropic)

    Note over User,Recorder: 测试执行阶段
    Test->>Web: 调用AI自动化方法
    Web->>Core: 创建Insight实例
    Core->>AI: 发送自然语言指令
    AI->>Core: 返回解析后的操作指令
    Core->>Web: 转换为具体操作
    Web->>Browser: 执行浏览器操作
    
    Note over User,Recorder: 元素定位阶段
    Web->>Core: 请求元素定位
    Core->>Shared: 使用图像处理工具
    Shared->>Browser: 获取页面截图
    Browser->>Shared: 返回页面截图
    Shared->>Core: 分析页面结构
    Core->>AI: 发送元素定位请求
    AI->>Core: 返回元素坐标
    Core->>Web: 返回定位结果

    Note over User,Recorder: 数据提取阶段
    Web->>Core: 请求数据提取
    Core->>AI: 发送数据提取指令
    AI->>Core: 返回提取策略
    Core->>Browser: 执行JavaScript提取
    Browser->>Core: 返回页面数据
    Core->>Web: 返回结构化数据

    Note over User,Recorder: 断言验证阶段
    Web->>Core: 请求断言验证
    Core->>AI: 发送断言指令
    AI->>Core: 返回验证结果
    Core->>Web: 返回断言结果
    Web->>Test: 返回测试结果

    Note over User,Recorder: 录制功能
    User->>Recorder: 启动录制
    Recorder->>Browser: 监听用户操作
    Browser->>Recorder: 捕获操作事件
    Recorder->>Shared: 处理录制数据
    Shared->>Recorder: 生成YAML脚本
    Recorder->>User: 输出测试脚本

    Note over User,Recorder: 报告生成
    Core->>Shared: 收集执行日志
    Shared->>Web: 生成测试报告
    Web->>Test: 返回详细报告
    Test->>User: 显示测试结果
```

## 组件详细分析

### 1. @midscene/core (核心AI引擎)
**主要功能：**
- AI指令解析和执行
- 任务调度和管理
- 上下文管理
- 多模态AI交互

**关键类：**
- `Executor`: 任务执行器
- `Insight`: AI洞察引擎
- `AI模型集成`: OpenAI/Anthropic支持

### 2. @midscene/web (Web自动化)
**主要功能：**
- Playwright/Puppeteer集成
- 浏览器自动化
- 测试报告生成
- Chrome扩展支持

**关键模块：**
- `playwright`: Playwright集成
- `puppeteer`: Puppeteer集成
- `playground`: 交互式测试环境
- `chrome-extension`: 浏览器扩展

### 3. @midscene/shared (共享工具库)
**主要功能：**
- 图像处理和分析
- 文件系统操作
- 工具函数
- 类型定义

**关键模块：**
- `img`: 图像处理
- `fs`: 文件系统
- `utils`: 工具函数
- `types`: 类型定义

### 4. @midscene/recorder (录制组件)
**主要功能：**
- 用户操作录制
- 脚本生成
- React UI界面

## 数据流分析

### 输入流程
1. **用户输入**: 自然语言指令
2. **AI解析**: 转换为结构化操作
3. **任务调度**: 创建执行任务
4. **浏览器操作**: 执行具体动作

### 输出流程
1. **数据收集**: 页面状态和操作结果
2. **AI分析**: 验证和断言
3. **报告生成**: 测试结果和日志
4. **用户反馈**: 显示执行结果

## 技术栈

### AI服务
- OpenAI GPT-4
- Anthropic Claude
- LangChain集成

### 浏览器自动化
- Playwright
- Puppeteer
- Chrome DevTools Protocol

### 图像处理
- Sharp
- Jimp
- Photon

### 开发工具
- TypeScript
- Vitest
- Modern.js构建工具

## 使用场景

1. **UI自动化测试**: 基于自然语言的测试脚本
2. **数据提取**: 智能网页数据抓取
3. **回归测试**: 自动化UI验证
4. **探索性测试**: AI辅助的测试发现
5. **脚本录制**: 用户操作自动转换为测试代码

这个架构设计使得Midscene能够提供强大的AI驱动的UI自动化能力，同时保持高度的可扩展性和易用性。 