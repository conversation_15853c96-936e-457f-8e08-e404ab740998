import { test } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { PlaywrightAiFixture } from '@midscene/web/playwright';

const testWithAI = test.extend<PlayWrightAiFixtureType>(PlaywrightAiFixture({
  waitForNetworkIdleTimeout: 2000,
}));

testWithAI.beforeEach(async ({ page }) => {
  await page.goto('https://www.baidu.com');
  await page.waitForLoadState('networkidle');
});

testWithAI('baidu_test', async ({
  ai,
  aiInput,
  aiTap,
  aiScroll,
  aiAssert,
  aiQuery,
  aiWaitFor,
  aiHover,
  aiKeyboardPress,
  page
}) => {
  // 自然语言指令: 输入邓紫棋，点击百度一下，校验是否有下一页
  await ai('输入邓紫棋，点击百度一下，校验是否有下一页');

  // 可以添加更多具体的验证步骤
  // await aiAssert('验证页面状态');
});
